# Fix fullscreen attack damage issue
$content = Get-Content "gm.js" -Raw

# First fix: line 298784
$content = $content -replace 'this\.mHitTargetList\.push\(_\);', 't.doValCheckHit(_, e, i, a) && this.mHitTargetList.push(_);'

# Second fix: line 298795  
$content = $content -replace '\(_\.entityId != e\.entityId \|\| this\.mConfig\.otherCamp\) && this\.mHitTargetList\.push\(_\);', '(_.entityId != e.entityId || this.mConfig.otherCamp) && t.doValCheckHit(_, e, i, a) && this.mHitTargetList.push(_);'

# Write back to file
$content | Set-Content "gm.js" -Encoding UTF8

Write-Host "Fullscreen attack damage fix completed!"
